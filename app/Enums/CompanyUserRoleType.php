<?php

namespace App\Enums;

enum CompanyUserRoleType: string
{
    case ADMIN = 'company-admin';
    case BILLING = 'company-billing';

    /**
     * Get the default role for new CompanyUsers
     */
    public static function getDefault(): self
    {
        return self::ADMIN;
    }

    /**
     * Get all available roles as array
     */
    public static function getAllRoles(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get role label for display
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::ADMIN => 'Company Administrator',
            self::BILLING => 'Company Billing Manager',
        };
    }
}
