<?php

namespace App\Enums;

/**
 * CompanyUser specific permissions
 * These are separate from regular User permissions
 */
enum CompanyUserPermissionType: string
{
    // Dashboard and basic access
    case COMPANY_DASHBOARD = 'company-dashboard';
    
    // Company management
    case COMPANY_PROFILE_VIEW = 'company-profile/view';
    case COMPANY_PROFILE_EDIT = 'company-profile/edit';
    case COMPANY_SETTINGS_VIEW = 'company-settings/view';
    case COMPANY_SETTINGS_EDIT = 'company-settings/edit';
    
    // User management within company
    case COMPANY_USERS_VIEW = 'company-users/view';
    case COMPANY_USERS_CREATE = 'company-users/create';
    case COMPANY_USERS_EDIT = 'company-users/edit';
    case COMPANY_USERS_DELETE = 'company-users/delete';
    
    // Billing and financial
    case COMPANY_BILLING_VIEW = 'company-billing/view';
    case COMPANY_BILLING_EDIT = 'company-billing/edit';
    case COMPANY_INVOICES_VIEW = 'company-invoices/view';
    case COMPANY_PAYMENTS_VIEW = 'company-payments/view';
    case COMPANY_PAYMENTS_MANAGE = 'company-payments/manage';
    
    // Campaigns and leads
    case COMPANY_CAMPAIGNS_VIEW = 'company-campaigns/view';
    case COMPANY_CAMPAIGNS_EDIT = 'company-campaigns/edit';
    case COMPANY_LEADS_VIEW = 'company-leads/view';
    
    // Reports
    case COMPANY_REPORTS_VIEW = 'company-reports/view';

    /**
     * Get permissions for admin role
     */
    public static function getAdminPermissions(): array
    {
        return [
            self::COMPANY_DASHBOARD->value,
            self::COMPANY_PROFILE_VIEW->value,
            self::COMPANY_PROFILE_EDIT->value,
            self::COMPANY_SETTINGS_VIEW->value,
            self::COMPANY_SETTINGS_EDIT->value,
            self::COMPANY_USERS_VIEW->value,
            self::COMPANY_USERS_CREATE->value,
            self::COMPANY_USERS_EDIT->value,
            self::COMPANY_USERS_DELETE->value,
            self::COMPANY_BILLING_VIEW->value,
            self::COMPANY_BILLING_EDIT->value,
            self::COMPANY_INVOICES_VIEW->value,
            self::COMPANY_PAYMENTS_VIEW->value,
            self::COMPANY_PAYMENTS_MANAGE->value,
            self::COMPANY_CAMPAIGNS_VIEW->value,
            self::COMPANY_CAMPAIGNS_EDIT->value,
            self::COMPANY_LEADS_VIEW->value,
            self::COMPANY_REPORTS_VIEW->value,
        ];
    }

    /**
     * Get permissions for billing role
     */
    public static function getBillingPermissions(): array
    {
        return [
            self::COMPANY_DASHBOARD->value,
            self::COMPANY_PROFILE_VIEW->value,
            self::COMPANY_BILLING_VIEW->value,
            self::COMPANY_BILLING_EDIT->value,
            self::COMPANY_INVOICES_VIEW->value,
            self::COMPANY_PAYMENTS_VIEW->value,
            self::COMPANY_PAYMENTS_MANAGE->value,
        ];
    }

    /**
     * Get all permissions as array
     */
    public static function getAllPermissions(): array
    {
        return array_column(self::cases(), 'value');
    }
}
